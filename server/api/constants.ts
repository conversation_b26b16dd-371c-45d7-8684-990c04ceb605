
import { IMap } from "./types/permission.types";

//* JWT
const JWT_SECRET = process.env.JWT_SECRET || "test-jwt-secret";
const URI = process.env.MONGO || "";
const SALT = 10;

//* Database Reference
const DB = {
  BANK: "Bank",
  COUNTER: "counter",
  TRANSACTION: "Transaction",
  DAILYBALANCE: "DailyBalance",
  CASHFLOW: "CashFlow",
  SALARY: "Salary",
  RETURN: "PurchaseReturn",
  RECIPECOSTING: "RecipeCosting",
  USER: "users",
  EMPSHIFT: "Shift",
  MENU: "MenuItems",
  MENUCATEGORY: "MenuCategory",
  EMPROLE: "EmpRole",
  TABLE: "Table",
  QRCODE: "QRCode",
  ORDER: "Order",
  FLOORPLAN: "floorplan",
  DEPARTMENT: "Department",
  POSITION: "Position",
  RESTAURANT: "Restaurant",
  PERMISSION: "Permission",
  ATTENDANCE: "Attendance",
  CREDIT: "Credit",
  CREDITREPAYMENTMETHOD: "CreditRepaymentMethod",
  CREDITREPAYMENTFREQUENCY: "CreditRepaymentFrequency",
  RESERVATION: "Reservation",
  RESERVATIONTYPE: "ReservationType",
  VENDOR: "Vendor",
  Expense: "Expense",
  ExpenseCategory: "ExpenseCategory",
  BUSINESSTYPE: "BusinessType",
  PAYMENTMODE: "PaymentMode",
  ITEM: "Item",
  ITEMTYPE: "ItemType",
  ITEMCATEGORY: "ItemCategory",
  STOCK: "Stock",
  SEMIPROCESSEDITEM: "SemiProcessedItem",
  SEMIPROCESSEDCATEGORY: "SemiProcessedCategory",
  SEMIPROCESSEDRECIPE: "SemiProcessedRecipe",
  PRODUCTIONBATCH: "ProductionBatch",
  SEMIPROCESSEDHISTORY: "SemiProcessedHistory",
  FOODSIZE: "FoodSize",
  BEVERAGESIZE: "BeverageSize",
  WASTESOURCE: "WasteSource",
  WASTETYPE: "WasteType",
  WASTEPRODUCTTYPE: "WasteProductType",
  PURCHASE: "Purchase",
  ITEMHISTORY: "ItemHistory",
  PROCESSINGRECIPE: "ProcessingRecipe",
  CLIENT: "Client",
  CLIENTCREDIT: "ClientCredit",
  CLIENTCREDITTRANSACTION: "ClientCreditTransaction",
  VENDORCREDIT: "VendorCredit",
  VENDORCREDITTRANSACTION: "VendorCreditTransaction",
  ORDERRETURN: "OrderReturn",
  DIRECTDISPOSAL: "DirectDisposal",
  CHEQUE: "Cheque",
  RESTAURANTOWNERSHIP: "RestaurantOwnership",
  FRANCHISE: "Franchise",
};

//* Counter Status
const COUNTERSTATUS = {
  ACTIVE: "active",
  INACTIVE: "inactive",
};

//* vendorStatus
const VENDORSTATUS = {
  PAID: "paid",
  DUE: "due",
  ADVANCE: "advance",
  OVERDUE: "overdue",
  IDLE: "idle",
};

//*User
const USERROLE = {
  SUPERADMIN: "superadmin",
  FRANCHISE_OWNER: "franchise_owner",
  ADMIN: "admin",
  USER: "user",
};
const SUPERADMIN = {
  name: "SuperAdmin",
  password: process.env.SUPER_ADMIN_PASS || "",
  email: process.env.SUPER_ADMIN_EMAIL || "",
};
const MAX_LOGO_SIZE_MB = 5;
const MAXUSERDOCSIZE = 5;
const ATTENDANCESTATUS = {
  PRESENT: "present",
  ABSENT: "absent",
  LATE: "late",
  LEAVE: "leave",
  ONGOING: "ongoing",
};

//* Credit
const CREDITSTATUS = {
  PAID: "paid",
  UNPAID: "unpaid",
  EXPIRED: "expired",
  REJECTED: "rejected",
};

//* Client Status
const CLIENTSTATUS = {
  ACTIVE: "active",
  INACTIVE: "inactive",
  SUSPENDED: "suspended",
};

//* Client Credit Status
const CLIENTCREDITSTATUS = {
  ACTIVE: "active",
  SUSPENDED: "suspended",
  OVERDUE: "overdue",
  CLOSED: "closed",
};

//* Vendor Credit Status
const VENDORCREDITSTATUS = {
  ACTIVE: "active",
  SUSPENDED: "suspended",
  OVERDUE: "overdue",
  CLOSED: "closed",
};

//* Cheque Status
const CHEQUESTATUS = {
  PENDING: "pending",
  CLEARED: "cleared",
  BOUNCED: "bounced",
  CANCELLED: "cancelled",
};

//* Cheque Type
const CHEQUETYPE = {
  RECEIVED: "received", // Cheque received from customer - needs to be deposited
  ISSUED: "issued",     // Cheque issued to vendor/supplier - needs to be cleared
};

//* Payment Terms
const PAYMENTTERMS = {
  WEEKLY: "weekly",
  MONTHLY: "monthly",
  YEARLY: "yearly",
  CUSTOM: "custom",
};

//* Table status
const TABLESTATUS = {
  AVAILABLE: "available",
  SEATED: "seated",
  WAITLIST: "waitlist",
  UNAVAILABLE: "unavailable",
};

//* Ingredient Types
const INGREDIENTTYPE = {
  RAW_MATERIAL: "raw_material",
  SEMI_PROCESSED: "semi_processed",
};

//* Operation Types for ItemHistory
const OPERATIONTYPE = {
  PURCHASE: "purchase",
  PRODUCTION: "production",
  ORDER_CONSUMPTION: "order_consumption",
  WASTE: "waste",
  RETURN: "return",
  ADJUSTMENT: "adjustment",
};

//* Order
const ORDERSTATUS = {
  SERVER: "server",
  READY: "ready",
  INPROGRESS: "inprogress",
  CANCEL: "cancel",
  COMPLAINT: "complaint",
};

const ORDERTYPE = {
  DINEIN: "dineIn",
  TAKEAWAY: "takeaway",
  DELIVERY: "delivery",
};

const PERMISSION_LEVELS = {
  NONE: 0, // 0000
  READ: 1 << 0, // 0001
  WRITE: 1 << 1, // 0010
  UPDATE: 1 << 2, // 0100
  DELETE: 1 << 3, // 1000
};

const PERMISSIONS = {
  NONE: "NONE",
  READ: "READ",
  CREATE: "WRITE",
  UPDATE: "UPDATE",
  DELETE: "DELETE",
};

//* Predefined permission combinations
const PERMISSION_COMBINATIONS = {
  NONE: PERMISSION_LEVELS.NONE, // 0
  READ_ONLY: PERMISSION_LEVELS.READ, // 1
  READ_WRITE: PERMISSION_LEVELS.READ | PERMISSION_LEVELS.WRITE, // 3
  READ_WRITE_UPDATE:
    PERMISSION_LEVELS.READ | PERMISSION_LEVELS.WRITE | PERMISSION_LEVELS.UPDATE, // 7
  FULL_ACCESS:
    PERMISSION_LEVELS.READ |
    PERMISSION_LEVELS.WRITE |
    PERMISSION_LEVELS.UPDATE |
    PERMISSION_LEVELS.DELETE, // 15
};

const PERMISSIONMODULES = {
  ManageOrders: "Manage Orders",
  TableReservation: "Table Reservation",
  MenuManagement: "Menu Management",
  InventoryManagement: "Inventory Management",
  DeliveryManagement: "Delivery Management",
  FinanceSales: "Finance & Sales",
  EmployeeManagement: "Employee Management",
  PermissionManagement: "Permission Management",
  OrderManagement: "Order Management",
  WasteManagement: "Waste Management",
  Reports: "Reports",
  Settings: "Settings",
  Franchise: "Franchise",
};

const PERMISSIONSUBMODULES = {
  Menu: "Menu",
  MenuCategory: "Menu Category",
  Employee: "Employee",
  Recipe: "Recipe and costing",
  ProcessingRecipe: "Processing Recipe",
  Production: "Production",
  Credit: "Credit",
  Attendance: "Attendance",
  Table: "Table",
  ReservationType: "Reservation Type",
  Reservation: "Reservation",
  Vendor: "Vendor",
  VendorCredit: "Vendor Credit",
  BusinessType: "Business Type",
  Expense: "Expense",
  ExpenseCategory: "Expense Category",
  Purchase: "Purchase",
  ProductionRate: "Production rate",
  Waste: "Waste",
  Permission: "Permission",
  Order: "Order",
};

const MODULE_SUBMODULE_MAP: IMap = {
  "Manage Orders": ["Order"],
  "Menu Management": ["Menu", "Menu Category", "Recipe and costing", "Processing Recipe"],
  "Inventory Management": [
    "Purchase",
    "Vendor",
    "Vendor Credit",
    "Production rate",
  ],
  "Delivery Management": [
    "Assign Delivery",
    "Track Delivery",
    "Manage Delivery Partners",
  ],
  "Finance & Sales": ["Expense", "Manage Invoices", "Refund Management"],
  "Employee Management": ["Attendance", "Employee", "Credit"],
  "Client Management": ["Client", "Client Credit", "Client Analytics"],
  Reports: ["Sales Report", "Inventory Report", "Employee Performance Report"],
  Settings: ["Employee", "Business Type", "Expense Category", "Purchase"],
  "Table Reservation": ["Table", "Reservation Type", "Reservation"],
  "Permission Management": ["Permission"],
  "Waste Management": ["Waste"],
};

//* To check valid object id of mongodb
const objectIdRegex = /^[0-9a-fA-F]{24}$/;

const corsOptions = {
  origin: [
    "http://localhost:5173",
    "http://localhost:3000",
    "http://localhost:8080",
    "https://akhabare.restaurantbilling.com",
    "https://www.akhabare.restaurantbilling.com",
    "https://fruitopia.restaurantbilling.com",
    "https://www.fruitopia.restaurantbilling.com",
    "https://khajasaja.restaurantbilling.com",
    "https://www.khajasaja.restaurantbilling.com"
    
  ],
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  allowedHeaders: ["Content-Type", "Authorization"],
};

export default {
  DB,
  JWT_SECRET,
  URI,
  COUNTERSTATUS,
  SALT,
  USERROLE,
  SUPERADMIN,
  MAX_LOGO_SIZE_MB,
  MAXUSERDOCSIZE,
  MODULE_SUBMODULE_MAP,
  ATTENDANCESTATUS,
  ORDERSTATUS,
  ORDERTYPE,
  CREDITSTATUS,
  CLIENTSTATUS,
  CLIENTCREDITSTATUS,
  VENDORCREDITSTATUS,
  CHEQUESTATUS,
  CHEQUETYPE,
  PAYMENTTERMS,
  PERMISSION_LEVELS,
  PERMISSIONS,
  PERMISSIONMODULES,
  PERMISSIONSUBMODULES,
  PERMISSION_COMBINATIONS,
  TABLESTATUS,
  VENDORSTATUS,
  INGREDIENTTYPE,
  OPERATIONTYPE,
  objectIdRegex,
  corsOptions,
};
