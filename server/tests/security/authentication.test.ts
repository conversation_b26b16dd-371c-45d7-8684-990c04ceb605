import request from "supertest";
import { app } from "../../app/app";
import { User } from "../../api/models/user.model";
import Restaurant from "../../api/models/restaurant.model";
import jwt from "jsonwebtoken";
import mongoose from "mongoose";
import constants from "../../api/constants";
import {setupTestEnvironment, teardownTestEnvironment,clearDatabase} from "../setup";

describe("Authentication Security Tests", () => {
  beforeAll(async () => {
    await setupTestEnvironment();
  });

  afterAll(async () => {
    await teardownTestEnvironment();
  });

  beforeEach(async () => {
    await clearDatabase();
  });

  describe("JWT Security", () => {
    let restaurant: any;
    let user: any;

    beforeEach(async () => {
      // Create test restaurant
      restaurant = await Restaurant.create({
        name: "Test Restaurant",
        phone: "1234567890",
        address: "123 Test St",
        logo: "https://test-logo.com/logo.png",
        subStart: new Date(),
        subEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      // Create test user
      user = await User.create({
        name: "Test User",
        email: "<EMAIL>",
        password: "TestPassword123!",
        userRole: "admin",
        restaurant: restaurant._id,
        isActive: true,
        gender: "Male",
      });
    });

    test("should reject requests with no token", async () => {
      const response = await request(app).get("/api/v1/en/restaurant").expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("Authentication required");
    });

    test("should reject requests with invalid token format", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", "InvalidToken")
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("Authentication required");
    });

    test("should reject requests with malformed JWT", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", "Bearer invalid.jwt.token")
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("invalid token");
    });

    test("should reject expired tokens", async () => {
      const expiredToken = jwt.sign(
        { id: user._id, userRole: "admin" },
        process.env.JWT_SECRET || 'test-jwt-secret',
        { expiresIn: "-1h" } // Expired 1 hour ago
      );

      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("invalid signature");
    });

    test("should reject tokens with wrong algorithm", async () => {
      const wrongAlgorithmToken = jwt.sign(
        { id: user._id, userRole: "admin" },
        process.env.JWT_SECRET || 'test-jwt-secret',
        { algorithm: "HS512" } // Wrong algorithm
      );

      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${wrongAlgorithmToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test("should reject tokens with missing required fields", async () => {
      const incompleteToken = jwt.sign(
        { id: user._id }, // Missing userRole and other required fields
        process.env.JWT_SECRET || 'test-jwt-secret',
        { expiresIn: "1h" }
      );

      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${incompleteToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
      // The auth middleware will return a generic error for incomplete tokens
      expect(response.body.message).toContain("Access denied");
    });

    test("should accept valid tokens", async () => {
      const validToken = jwt.sign(
        {
          id: user._id.toString(), // Use 'id' field like the auth controller does
          email: user.email,
          userRole: "admin",
          restaurant: user.restaurant,
          exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
        },
        process.env.JWT_SECRET || 'test-jwt-secret'
      );

      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${validToken}`);

      // Should not be 401 (might be other errors due to missing data, but auth should pass)
      expect(response.status).not.toBe(401);
    });
  });

  describe("Rate Limiting", () => {
    test("should enforce rate limits on login attempts", async () => {
      const loginData = {
        email: "<EMAIL>",
        password: "wrongpassword",
      };

      // Make multiple failed login attempts
      const promises = Array(6)
        .fill(null)
        .map(() => request(app).post("/api/v1/en/auth/login").send(loginData));

      const responses = await Promise.all(promises);

      // Last request should be rate limited
      const lastResponse = responses[responses.length - 1];
      expect(lastResponse.status).toBe(429);
      expect(lastResponse.body.message).toContain("Too many");
    });

    test("should enforce general API rate limits", async () => {
      const token = jwt.sign(
        {
          id: new mongoose.Types.ObjectId().toString(), // Use 'id' field like the auth controller
          userRole: "admin",
          email: "<EMAIL>",
          restaurant: new mongoose.Types.ObjectId(),
          exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
        },
        process.env.JWT_SECRET || 'test-jwt-secret'
      );

      // Make many requests quickly
      const promises = Array(101)
        .fill(null)
        .map(() =>
          request(app)
            .get("/api/v1/en/restaurant")
            .set("Authorization", `Bearer ${token}`)
        );

      const responses = await Promise.all(promises);

      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter((r) => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe("Input Validation Security", () => {
    let validToken: string;
    let testUser: any;

    beforeEach(async () => {
      // Create test user for this describe block
      const restaurant = await Restaurant.create({
        name: "Test Restaurant",
        phone: "1234567890",
        address: "123 Test St",
        logo: "https://test-logo.com/logo.png",
        subStart: new Date(),
        subEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      testUser = await User.create({
        name: "Test User",
        email: "<EMAIL>",
        password: "TestPassword123!",
        userRole: "admin",
        restaurant: restaurant._id,
        isActive: true,
        gender: "Male",
      });

      validToken = jwt.sign(
        {
          id: testUser._id.toString(), // Use 'id' field like the auth controller
          userRole: "admin",
          email: testUser.email,
          restaurant: testUser.restaurant,
          exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
        },
        process.env.JWT_SECRET || 'test-jwt-secret'
      );
    });

    test("should sanitize XSS attempts in request body", async () => {
      const maliciousData = {
        name: '<script>alert("xss")</script>Test Restaurant',
        email: "<EMAIL>",
        description: 'javascript:alert("xss")',
      };

      const response = await request(app)
        .post("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${validToken}`)
        .send(maliciousData);

      // Should not contain script tags or javascript: protocol
      if (response.body.data) {
        expect(response.body.data.name).not.toContain("<script>");
        expect(response.body.data.description).not.toContain("javascript:");
      }
    });

    test("should reject invalid ObjectId formats", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant/invalid-id")
        .set("Authorization", `Bearer ${validToken}`);

      // Should pass authentication but may fail on other validation
      expect(response.status).not.toBe(401);
      expect(response.body.success).toBe(false);
    });

    test("should validate email formats", async () => {
      const invalidEmailData = {
        name: "Test User",
        email: "invalid-email-format",
        password: "TestPassword123!",
      };

      const response = await request(app)
        .post("/api/v1/en/auth/register")
        .send(invalidEmailData);

      // Registration doesn't require authentication anymore
      expect(response.body.success).toBe(false);
    });

    test("should enforce password strength requirements", async () => {
      const weakPasswordData = {
        name: "Test User",
        email: "<EMAIL>",
        password: "123", // Too weak
      };

      const response = await request(app)
        .post("/api/v1/en/auth/register")
        .send(weakPasswordData);

      // Registration doesn't require authentication anymore
      expect(response.body.success).toBe(false);
    });
  });

  describe("Authorization Security", () => {
    let adminUser: any;
    let regularUser: any;
    let adminToken: string;
    let userToken: string;

    beforeEach(async () => {
      const restaurant = await Restaurant.create({
        name: "Test Restaurant",
        phone: "1234567890",
        address: "123 Test St",
        logo: "https://test-logo.com/logo.png",
        subStart: new Date(),
        subEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      adminUser = await User.create({
        name: "Admin User",
        email: "<EMAIL>",
        password: "AdminPassword123!",
        userRole: "admin",
        restaurant: restaurant._id,
        isActive: true,
        gender: "Male",
      });

      regularUser = await User.create({
        name: "Regular User",
        email: "<EMAIL>",
        password: "UserPassword123!",
        userRole: "user",
        restaurant: restaurant._id,
        isActive: true,
        gender: "Female",
      });

      adminToken = jwt.sign(
        {
          id: adminUser._id.toString(), // Use 'id' field like the auth controller
          userRole: "admin",
          email: adminUser.email,
          restaurant: adminUser.restaurant,
          exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
        },
        process.env.JWT_SECRET || 'test-jwt-secret'
      );
      userToken = jwt.sign(
        {
          id: regularUser._id.toString(), // Use 'id' field like the auth controller
          userRole: "waiter",
          email: regularUser.email,
          restaurant: regularUser.restaurant,
          exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
        },
        process.env.JWT_SECRET || 'test-jwt-secret'
      );
    });

    test("should allow admin access to admin-only endpoints", async () => {
      const response = await request(app)
        .post("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${adminToken}`)
        .send({
          name: "New Restaurant",
          phone: "9876543210",
          address: "456 New St",
          logo: "https://test-logo.com/logo.png",
          subStart: new Date(),
          subEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        });

      // Should not be 403 (forbidden)
      expect(response.status).not.toBe(403);
    });

    test("should deny regular user access to admin-only endpoints", async () => {
      const response = await request(app)
        .post("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${userToken}`)
        .send({
          name: "New Restaurant",
          phone: "9876543210",
          address: "456 New St",
          logo: "https://test-logo.com/logo.png",
          subStart: new Date(),
          subEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        });

      // Should be 401 (unauthorized) since the user doesn't have proper authentication
      // or 403 (forbidden) if authentication passes but authorization fails
      expect([401, 403]).toContain(response.status);
      expect(response.body.success).toBe(false);
    });
  });

  describe("Session Security", () => {
    let sessionUser: any;

    beforeEach(async () => {
      const restaurant = await Restaurant.create({
        name: "Session Test Restaurant",
        phone: "1234567890",
        address: "123 Session St",
        logo: "https://test-logo.com/logo.png",
        subStart: new Date(),
        subEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      sessionUser = await User.create({
        name: "Session User",
        email: "<EMAIL>",
        password: "SessionPassword123!",
        userRole: "admin",
        restaurant: restaurant._id,
        isActive: true,
        gender: "Other",
      });
    });

    test("should handle concurrent login attempts", async () => {
      const loginData = {
        email: sessionUser.email,
        password: "SessionPassword123!",
      };

      // Simulate concurrent login attempts
      const promises = Array(5)
        .fill(null)
        .map(() => request(app).post("/api/v1/en/auth/login").send(loginData));

      const responses = await Promise.all(promises);

      // All should either succeed or fail gracefully (no crashes)
      responses.forEach((response) => {
        expect([200, 401, 429]).toContain(response.status);
      });
    });

    test("should invalidate tokens on logout", async () => {
      // Login first
      const loginResponse = await request(app).post("/api/v1/en/auth/login").send({
        email: sessionUser.email,
        password: "SessionPassword123!",
      });

      const token = loginResponse.body.data?.accessToken;
      expect(token).toBeDefined();

      // Logout
      await request(app)
        .post("/api/v1/en/auth/logout")
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      // Token should still work (stateless JWT - would need token blacklist for true invalidation)
      // This test documents current behavior
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${token}`);

      // Currently tokens remain valid after logout (stateless)
      // In production, implement token blacklist or short expiry
      expect(response.status).not.toBe(401);
    });
  });
});
